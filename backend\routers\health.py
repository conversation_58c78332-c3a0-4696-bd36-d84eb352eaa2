"""
Health check endpoints
"""
from fastapi import APIRouter
from config import success_response

router = APIRouter()

@router.get("/")
@router.get("/health")
async def health_check():
    """Health check endpoint"""
    return success_response(
        data={
            "status": "healthy",
            "service": "Healthcare Data Sharing API",
            "version": "1.0.0"
        },
        message="Service is running"
    )
