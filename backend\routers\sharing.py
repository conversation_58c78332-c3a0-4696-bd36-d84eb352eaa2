"""
Record sharing endpoints
"""
import os
import hashlib
import base64
from fastapi import API<PERSON><PERSON><PERSON>, Body, Depends
from typing import Dict, Any

from backend.models.sharing import ShareRequest, ShareResponse, DecryptRequest
from backend.config import success_response, error_response, PATIENT_ADDRESS, DOCTOR_ADDRESS
from backend.data import encrypt_record, decrypt_record
from backend.services.key_manager import KeyManager
from backend.services.ipfs_service import IPFSService

router = APIRouter()

def get_key_manager() -> KeyManager:
    """Dependency to get key manager"""
    return KeyManager()

def get_ipfs_service() -> IPFSService:
    """Dependency to get IPFS service"""
    return IPFSService()

@router.post("/share")
async def share_record(
    record_cid: str = Body(None),
    doctor_address: str = Body(None),
    wallet_address: str = Body(...),
    share_req: ShareRequest = None,
    key_manager: KeyManager = Depends(get_key_manager),
    ipfs_service: IPFSService = Depends(get_ipfs_service)
):
    """
    <PERSON><PERSON> shares a record with a doctor via IPFS (off-chain)
    """
    try:
        # Handle both parameter formats
        if share_req is None and record_cid is not None and doctor_address is not None:
            actual_record_cid = record_cid
            actual_doctor_address = doctor_address
        elif share_req is not None:
            actual_record_cid = share_req.record_cid
            actual_doctor_address = share_req.doctor_address
        else:
            return error_response("Missing required parameters: record_cid and doctor_address", 400)

        # Check if the wallet address matches the Patient address
        if wallet_address == PATIENT_ADDRESS:
            print(f"✅ Patient {wallet_address} sharing record with doctor {actual_doctor_address}")
        else:
            print(f"⚠️ Non-patient address {wallet_address} attempting to share a record")

        # Check if it's a valid doctor address
        if actual_doctor_address != DOCTOR_ADDRESS:
            print(f"⚠️ Sharing with non-doctor address {actual_doctor_address}")

        # 1. Retrieve and decrypt the original record
        print(f"📖 Retrieving record {actual_record_cid}")

        # Try IPFS first, then local storage
        original_record = ipfs_service.get_data(actual_record_cid)
        if not original_record:
            return error_response(f"Record not found: {actual_record_cid}", 404)

        # Generate patient key deterministically
        patient_key = hashlib.sha256(f"{wallet_address}_key".encode()).digest()
        print(f"🔑 Generated patient key: {patient_key[:5].hex()}...")

        # Decrypt the record
        try:
            decrypted_record = decrypt_record(original_record, patient_key)
            print(f"🔓 Successfully decrypted record")
        except Exception as decrypt_error:
            print(f"❌ Error decrypting record: {str(decrypt_error)}")
            error_response(f"Error decrypting record: {str(decrypt_error)}", 500)

        # 2. Generate temporary key and re-encrypt
        temp_key = os.urandom(32)
        print(f"🔑 Generated temporary key: {temp_key[:5].hex()}...")

        try:
            re_encrypted_record = encrypt_record(decrypted_record, temp_key)
            print(f"🔒 Re-encrypted record: {len(re_encrypted_record)} bytes")
        except Exception as enc_error:
            print(f"❌ Error re-encrypting record: {str(enc_error)}")
            return error_response(f"Error re-encrypting record: {str(enc_error)}", 500)

        # 3. Upload re-encrypted record to IPFS
        try:
            cid_share = ipfs_service.add_data(re_encrypted_record)
            print(f"📦 Uploaded shared record to IPFS: {cid_share}")
        except Exception as ipfs_error:
            print(f"❌ Error uploading to IPFS: {str(ipfs_error)}")
            return error_response(f"Error uploading to IPFS: {str(ipfs_error)}", 500)

        # 4. Encrypt temporary key with doctor's public key
        try:
            doctor_public_key = key_manager.get_public_key(actual_doctor_address)
            if not doctor_public_key:
                error_response(f"Doctor public key not found for address: {actual_doctor_address}", 404)

            print(f"🔑 Retrieved doctor's public key for encryption")

            # Encrypt the temporary key with doctor's public key using RSA-OAEP
            encrypted_key_bytes = key_manager.encrypt_with_public_key(temp_key, actual_doctor_address)
            encrypted_key = base64.b64encode(encrypted_key_bytes).decode('utf-8')
            print(f"🔒 Successfully encrypted temp key: {len(encrypted_key)} chars")

        except Exception as encrypt_error:
            print(f"❌ Error encrypting with doctor's public key: {str(encrypt_error)}")
            return error_response(f"Error encrypting temporary key: {str(encrypt_error)}", 500)

        return success_response(
            data={
                "shared_cid": cid_share,
                "encrypted_key": encrypted_key,
                "doctor_address": actual_doctor_address,
                "success": True,
                "message": "Record shared successfully"
            }
        )

    except Exception as e:
        return error_response(str(e), 500)

@router.post("/decrypt")
async def decrypt_shared_record(
    data: Dict[str, Any] = Body(...),
    key_manager: KeyManager = Depends(get_key_manager),
    ipfs_service: IPFSService = Depends(get_ipfs_service)
):
    """
    Doctor decrypts a shared record using their private key
    """
    try:
        shared_cid = data.get("shared_cid", "")
        encrypted_key = data.get("encrypted_key", "")
        wallet_address = data.get("wallet_address", "")

        # Validate inputs
        if not shared_cid or not encrypted_key or not wallet_address:
            error_response("Missing required fields", 400)

        # Check if the wallet address matches the Doctor address
        if wallet_address == DOCTOR_ADDRESS:
            print(f"✅ Doctor {wallet_address} decrypting shared record")
        else:
            print(f"⚠️ Non-doctor address {wallet_address} attempting to decrypt shared record")

        # 1. Retrieve the shared encrypted record
        shared_record = ipfs_service.get_data(shared_cid)
        if not shared_record:
            error_response(f"Shared record not found: {shared_cid}", 404)

        # 2. Decrypt the temporary key with doctor's private key
        try:
            encrypted_key_bytes = base64.b64decode(encrypted_key.encode('utf-8'))
            temp_key = key_manager.decrypt_with_private_key(encrypted_key_bytes, wallet_address)
            print(f"🔓 Successfully decrypted temporary key")
        except Exception as decrypt_error:
            print(f"❌ Error decrypting temporary key: {str(decrypt_error)}")
            error_response(f"Error decrypting temporary key: {str(decrypt_error)}", 500)

        # 3. Decrypt the record with the temporary key
        try:
            decrypted_record = decrypt_record(shared_record, temp_key)
            print(f"🔓 Successfully decrypted shared record")

            return success_response(data=decrypted_record)
        except Exception as decrypt_error:
            print(f"❌ Error decrypting shared record: {str(decrypt_error)}")
            error_response(f"Error decrypting shared record: {str(decrypt_error)}", 500)

    except Exception as e:
        error_response(str(e), 500)
