"""
Main FastAPI application entry point
"""
import os
from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware

# Import routers
from routers import auth, records, sharing, purchasing, templates, verification, health

# Import services for initialization
from services.key_manager import KeyManager
from services.ipfs_service import IPFSService

# Global service instances
_key_manager: KeyManager = None
_ipfs_service: IPFSService = None

def get_key_manager() -> KeyManager:
    """Dependency to get key manager instance"""
    global _key_manager
    if _key_manager is None:
        _key_manager = KeyManager()
    return _key_manager

def get_ipfs_service() -> IPFSService:
    """Dependency to get IPFS service instance"""
    global _ipfs_service
    if _ipfs_service is None:
        _ipfs_service = IPFSService()
    return _ipfs_service

def create_app() -> FastAPI:
    """Create and configure the FastAPI application"""

    app = FastAPI(
        title="Healthcare Data Sharing API",
        description="A secure healthcare data sharing system using blockchain and IPFS",
        version="1.0.0"
    )

    # Enable CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include routers
    app.include_router(health.router, tags=["Health"])
    app.include_router(auth.router, prefix="/auth", tags=["Authentication"])
    app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
    app.include_router(records.router, prefix="/api/records", tags=["Records"])
    app.include_router(records.router, prefix="/records", tags=["Records"])
    app.include_router(sharing.router, prefix="/api", tags=["Sharing"])
    app.include_router(sharing.router, tags=["Sharing"])
    app.include_router(purchasing.router, prefix="/api", tags=["Purchasing"])
    app.include_router(purchasing.router, tags=["Purchasing"])
    app.include_router(templates.router, prefix="/api", tags=["Templates"])
    app.include_router(templates.router, tags=["Templates"])
    app.include_router(verification.router, prefix="/api", tags=["Verification"])
    app.include_router(verification.router, tags=["Verification"])

    return app

# Create the app instance
app = create_app()

# Initialize services on startup
@app.on_event("startup")
async def startup_event():
    """Initialize services when the application starts"""
    try:
        # Initialize services
        global _key_manager, _ipfs_service
        _key_manager = KeyManager()
        _ipfs_service = IPFSService()

        print("✅ Application startup complete")
        print(f"🔑 Key Manager initialized")
        print(f"📦 IPFS Service initialized (Connected: {_ipfs_service.is_connected()})")
    except Exception as e:
        print(f"❌ Error during startup: {str(e)}")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup when the application shuts down"""
    print("🔄 Application shutdown")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "backend.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
