"""
Gas fees and transaction cost endpoints
"""
import os
import json
import time
from fastapi import APIRouter, Query
from typing import Optional

from backend.config import success_response, error_response, LOCAL_STORAGE_PATH

router = APIRouter()

@router.get("/fees")
@router.get("/gas-fees")
async def get_fees(
    wallet_address: Optional[str] = Query(None),
    workflow: Optional[str] = Query(None)
):
    """
    Get gas fees and transaction fees for the product
    """
    try:
        # Check if the local storage directory exists
        if not os.path.exists("local_storage/purchases") and not os.path.exists("local_storage/transactions"):
            return {
                "total_gas_fees": 0,
                "transaction_count": 0,
                "average_gas_fee": 0,
                "fees_by_type": {},
                "fees_by_workflow": {},
                "transactions": []
            }

        # Initialize transaction list
        transactions = []

        # Load transactions from purchases directory
        purchases_dir = "local_storage/purchases"
        if os.path.exists(purchases_dir):
            for filename in os.listdir(purchases_dir):
                if filename.endswith(".json"):
                    file_path = os.path.join(purchases_dir, filename)
                    try:
                        with open(file_path, "r") as f:
                            purchase_data = json.load(f)
                        
                        # Create transaction record from purchase data
                        tx = {
                            "id": f"purchase_{purchase_data.get('request_id', filename)}",
                            "type": "Purchase Request",
                            "status": purchase_data.get("status", "unknown"),
                            "timestamp": purchase_data.get("created_at", time.time()),
                            "gas_fee": 0.002,  # Estimated gas fee for purchase
                            "wallet_address": purchase_data.get("buyer_address", "unknown"),
                            "details": {
                                "request_id": purchase_data.get("request_id"),
                                "template_cid": purchase_data.get("template_cid"),
                                "buyer_address": purchase_data.get("buyer_address")
                            }
                        }
                        transactions.append(tx)
                    except Exception as e:
                        print(f"Error loading purchase file {filename}: {str(e)}")

        # Load transactions from transactions directory
        transactions_dir = "local_storage/transactions"
        if os.path.exists(transactions_dir):
            for filename in os.listdir(transactions_dir):
                if filename.endswith(".json"):
                    file_path = os.path.join(transactions_dir, filename)
                    try:
                        with open(file_path, "r") as f:
                            tx_data = json.load(f)
                        transactions.append(tx_data)
                    except Exception as e:
                        print(f"Error loading transaction file {filename}: {str(e)}")

        # Load transactions from workflow-specific directories
        for workflow_name in ["storing", "sharing", "purchasing", "other"]:
            workflow_dir = f"local_storage/{workflow_name}_transactions"
            if os.path.exists(workflow_dir):
                for filename in os.listdir(workflow_dir):
                    if filename.endswith(".json"):
                        file_path = os.path.join(workflow_dir, filename)
                        try:
                            with open(file_path, "r") as f:
                                tx_data = json.load(f)
                            transactions.append(tx_data)
                        except Exception as e:
                            print(f"Error loading {workflow_name} transaction file {filename}: {str(e)}")

        # Remove duplicates (based on transaction ID)
        unique_transactions = {}
        for tx in transactions:
            tx_id = tx.get("id")
            if tx_id and tx_id not in unique_transactions:
                unique_transactions[tx_id] = tx

        # Convert back to list
        transactions = list(unique_transactions.values())

        # Sort transactions by timestamp (newest first)
        transactions.sort(key=lambda x: x.get("timestamp", 0), reverse=True)

        # Calculate total gas fees
        total_gas_fees = sum(tx.get("gas_fee", 0) for tx in transactions)
        transaction_count = len(transactions)
        average_gas_fee = total_gas_fees / transaction_count if transaction_count > 0 else 0

        # Workflow mapping
        workflow_mapping = {
            "Store Record": "storing",
            "Sign Record": "storing", 
            "Share Record": "sharing",
            "Decrypt Shared Record": "sharing",
            "Purchase Request": "purchasing",
            "Fill Template": "purchasing",
            "Finalize": "purchasing"
        }

        # Add workflow category to each transaction
        for tx in transactions:
            tx_type = tx.get("type", "Unknown")
            tx["workflow"] = workflow_mapping.get(tx_type, "other")

        # Filter by workflow if specified
        if workflow:
            transactions = [tx for tx in transactions if tx.get("workflow") == workflow]

        # Calculate fees by transaction type
        fees_by_type = {}
        for tx in transactions:
            tx_type = tx.get("type", "Unknown")
            gas_fee = tx.get("gas_fee", 0)
            if tx_type not in fees_by_type:
                fees_by_type[tx_type] = {
                    "count": 0,
                    "total_gas_fee": 0,
                    "average_gas_fee": 0
                }
            fees_by_type[tx_type]["count"] += 1
            fees_by_type[tx_type]["total_gas_fee"] += gas_fee

        # Calculate fees by workflow
        fees_by_workflow = {
            "storing": {"count": 0, "total_gas_fee": 0, "average_gas_fee": 0},
            "sharing": {"count": 0, "total_gas_fee": 0, "average_gas_fee": 0},
            "purchasing": {"count": 0, "total_gas_fee": 0, "average_gas_fee": 0},
            "other": {"count": 0, "total_gas_fee": 0, "average_gas_fee": 0}
        }

        for tx in transactions:
            workflow_type = tx.get("workflow", "other")
            gas_fee = tx.get("gas_fee", 0)
            fees_by_workflow[workflow_type]["count"] += 1
            fees_by_workflow[workflow_type]["total_gas_fee"] += gas_fee

        # Calculate average gas fee by type
        for tx_type, data in fees_by_type.items():
            data["average_gas_fee"] = data["total_gas_fee"] / data["count"] if data["count"] > 0 else 0

        # Calculate average gas fee by workflow
        for workflow_type, data in fees_by_workflow.items():
            data["average_gas_fee"] = data["total_gas_fee"] / data["count"] if data["count"] > 0 else 0

        # Calculate additional statistics
        min_gas_fee = min([tx.get("gas_fee", 0) for tx in transactions]) if transactions else 0
        max_gas_fee = max([tx.get("gas_fee", 0) for tx in transactions]) if transactions else 0
        first_tx_time = min([tx.get("timestamp", 0) for tx in transactions]) if transactions else 0
        last_tx_time = max([tx.get("timestamp", 0) for tx in transactions]) if transactions else 0

        # Create workflow-specific directories if they don't exist
        for wf in ["storing", "sharing", "purchasing", "other"]:
            os.makedirs(f"local_storage/{wf}_transactions", exist_ok=True)

        # Return the result with additional statistics
        return {
            "total_gas_fees": round(total_gas_fees, 6),
            "transaction_count": transaction_count,
            "average_gas_fee": round(average_gas_fee, 6),
            "fees_by_type": fees_by_type,
            "fees_by_workflow": fees_by_workflow,
            "transactions": transactions,
            "stats": {
                "min_gas_fee": round(min_gas_fee, 6),
                "max_gas_fee": round(max_gas_fee, 6),
                "first_transaction_time": first_tx_time,
                "last_transaction_time": last_tx_time,
                "workflow_filter": workflow if workflow else "all",
                "wallet_address": wallet_address if wallet_address else "all"
            }
        }
    except Exception as e:
        print(f"Error in get_fees: {str(e)}")
        import traceback
        traceback.print_exc()
        return error_response(str(e), 500)

@router.get("/basescan/gas-price")
async def get_basescan_gas_price():
    """Get current gas price from Basescan API"""
    try:
        # For demo purposes, return mock gas price data
        # In production, this would call the actual Basescan API
        mock_gas_price = 1000000000  # 1 Gwei in wei
        
        return success_response(
            data={
                "gas_price_wei": mock_gas_price,
                "gas_price_gwei": 1.0,
                "timestamp": int(time.time())
            },
            message="Successfully retrieved gas price (mock data)"
        )
    except Exception as e:
        return error_response(str(e), 500)
