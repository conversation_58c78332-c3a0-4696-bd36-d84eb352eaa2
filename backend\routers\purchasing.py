"""
Data purchasing workflow endpoints
"""
import os
import json
import time
import hashlib
from fastapi import APIRouter, Body, Depends
from typing import Dict, Any, List

from models.purchasing import (
    PurchaseRequest, PurchaseResponse, BuyerRequest,
    HospitalConfirmation, PatientFillTemplate, PurchaseVerification
)
from config import (
    success_response, error_response, LOCAL_STORAGE_PATH,
    BUYER_ADDRESS, HOSPITAL_ADDRESS, PATIENT_ADDRESS
)
from services.ipfs_service import IPFSService

router = APIRouter()

def get_ipfs_service() -> IPFSService:
    """Dependency to get IPFS service"""
    return IPFSService()

def clean_cid(cid: str) -> str:
    """Clean CID by removing quotes and whitespace"""
    if not cid:
        return cid
    return cid.strip().strip('"').strip("'")

@router.post("/purchase/request")
async def create_purchase_request(
    template: Dict[str, Any] = Body(...),
    wallet_address: str = Body(...),
    ipfs_service: IPFSService = Depends(get_ipfs_service)
):
    """
    Buyer creates a purchase request with template
    """
    try:
        # Check if the wallet address matches the Buyer address
        if wallet_address == BUYER_ADDRESS:
            print(f"✅ Buyer {wallet_address} creating purchase request")
        else:
            print(f"⚠️ Non-buyer address {wallet_address} attempting to create purchase request")

        # Generate a unique request ID
        request_id = f"req_{int(time.time())}_{hashlib.sha256(wallet_address.encode()).hexdigest()[:8]}"

        # Store template on IPFS
        template_json = json.dumps(template).encode()
        template_cid = ipfs_service.add_data(template_json)
        print(f"📦 Template stored with CID: {template_cid}")

        # Create purchase request data
        purchase_data = {
            "request_id": request_id,
            "buyer_address": wallet_address,
            "template": template,
            "template_cid": template_cid,
            "status": "pending_hospital_confirmation",
            "created_at": time.time(),
            "updated_at": time.time()
        }

        # Store purchase request locally
        purchases_dir = os.path.join(LOCAL_STORAGE_PATH, "purchases")
        os.makedirs(purchases_dir, exist_ok=True)
        
        request_file = os.path.join(purchases_dir, f"{request_id}.json")
        with open(request_file, "w") as f:
            json.dump(purchase_data, f, indent=2)

        print(f"✅ Purchase request created: {request_id}")

        return success_response(
            data={
                "request_id": request_id,
                "status": "pending_hospital_confirmation",
                "template_cid": template_cid,
                "message": "Purchase request created successfully"
            }
        )

    except Exception as e:
        error_response(str(e), 500)

@router.post("/purchase/confirm")
async def confirm_purchase_request(
    request_id: str = Body(...),
    wallet_address: str = Body(...),
    confirmed: bool = Body(True)
):
    """
    Hospital confirms a purchase request
    """
    try:
        # Check if the wallet address matches the Hospital address
        if wallet_address == HOSPITAL_ADDRESS:
            print(f"✅ Hospital {wallet_address} confirming purchase request")
        else:
            print(f"⚠️ Non-hospital address {wallet_address} attempting to confirm purchase request")

        # Load the purchase request
        request_file = os.path.join(LOCAL_STORAGE_PATH, "purchases", f"{request_id}.json")
        if not os.path.exists(request_file):
            error_response(f"Purchase request {request_id} not found", 404)

        with open(request_file, "r") as f:
            purchase_data = json.load(f)

        # Update the status
        if confirmed:
            purchase_data["status"] = "pending_patient_data"
            purchase_data["hospital_confirmed_at"] = time.time()
            purchase_data["hospital_address"] = wallet_address
            message = "Purchase request confirmed by hospital"
        else:
            purchase_data["status"] = "rejected_by_hospital"
            purchase_data["hospital_rejected_at"] = time.time()
            purchase_data["hospital_address"] = wallet_address
            message = "Purchase request rejected by hospital"

        purchase_data["updated_at"] = time.time()

        # Save updated request
        with open(request_file, "w") as f:
            json.dump(purchase_data, f, indent=2)

        print(f"✅ Purchase request {request_id} status updated to: {purchase_data['status']}")

        return success_response(
            data={
                "request_id": request_id,
                "status": purchase_data["status"],
                "confirmed": confirmed,
                "message": message
            }
        )

    except Exception as e:
        error_response(str(e), 500)

@router.get("/purchase/requests")
async def get_purchase_requests(wallet_address: str):
    """
    Get purchase requests (filtered by role)
    """
    try:
        requests = []
        purchases_dir = os.path.join(LOCAL_STORAGE_PATH, "purchases")
        
        if not os.path.exists(purchases_dir):
            return success_response(data={"requests": []})

        # Load all purchase requests
        for filename in os.listdir(purchases_dir):
            if filename.endswith(".json"):
                request_file = os.path.join(purchases_dir, filename)
                try:
                    with open(request_file, "r") as f:
                        purchase_data = json.load(f)
                    
                    # Filter based on wallet address role
                    if wallet_address == BUYER_ADDRESS:
                        # Buyer sees their own requests
                        if purchase_data.get("buyer_address") == wallet_address:
                            requests.append(purchase_data)
                    elif wallet_address == HOSPITAL_ADDRESS:
                        # Hospital sees requests pending confirmation
                        if purchase_data.get("status") == "pending_hospital_confirmation":
                            requests.append(purchase_data)
                    elif wallet_address == PATIENT_ADDRESS:
                        # Patient sees confirmed requests pending their data
                        if purchase_data.get("status") == "pending_patient_data":
                            requests.append(purchase_data)
                    else:
                        # Other addresses see all requests (for demo)
                        requests.append(purchase_data)
                        
                except Exception as e:
                    print(f"⚠️ Error loading request file {filename}: {str(e)}")

        # Sort by creation time (newest first)
        requests.sort(key=lambda x: x.get("created_at", 0), reverse=True)

        return success_response(data={"requests": requests})

    except Exception as e:
        error_response(str(e), 500)

@router.post("/patient/fill-template")
async def fill_template(
    request_id: str = Body(...),
    wallet_address: str = Body(...),
    ipfs_service: IPFSService = Depends(get_ipfs_service)
):
    """
    Patient fills a template for a purchase request
    """
    try:
        print(f"📝 Patient {wallet_address} filling template for request {request_id}")

        # Check if the request exists
        request_file = os.path.join(LOCAL_STORAGE_PATH, "purchases", f"{request_id}.json")
        if not os.path.exists(request_file):
            error_response(f"Purchase request {request_id} not found", 404)

        # Load the request data
        with open(request_file, "r") as f:
            purchase_data = json.load(f)

        # Check if the request is in the correct status
        if purchase_data.get("status") != "pending_patient_data":
            error_response(f"Purchase request {request_id} is not ready for patient data", 400)

        # Auto-fill template with mock patient data
        template = purchase_data.get("template", {})
        filled_template = {
            "patientId": wallet_address,
            "patientName": "John Doe",
            "age": 35,
            "diagnosis": "Hypertension",
            "treatment": "Medication",
            "bloodPressure": "140/90",
            "heartRate": "75 bpm",
            "weight": "70 kg",
            "height": "175 cm",
            "allergies": "None",
            "medications": ["Lisinopril", "Hydrochlorothiazide"],
            "lastVisit": "2024-01-15",
            "doctorNotes": "Patient responding well to treatment",
            "template_id": template.get("template_id", "general_health"),
            "filled_at": time.time(),
            "request_id": request_id
        }

        # Store filled template on IPFS
        filled_template_json = json.dumps(filled_template).encode()
        filled_template_cid = ipfs_service.add_data(filled_template_json)
        print(f"📦 Filled template stored with CID: {filled_template_cid}")

        # Update purchase request
        purchase_data["status"] = "completed"
        purchase_data["filled_template"] = filled_template
        purchase_data["filled_template_cid"] = filled_template_cid
        purchase_data["patient_filled_at"] = time.time()
        purchase_data["patient_address"] = wallet_address
        purchase_data["updated_at"] = time.time()

        # Save updated request
        with open(request_file, "w") as f:
            json.dump(purchase_data, f, indent=2)

        print(f"✅ Template filled for request {request_id}")

        return success_response(
            data={
                "request_id": request_id,
                "status": "completed",
                "filled_template_cid": filled_template_cid,
                "filled_template": filled_template,
                "message": "Template filled successfully"
            }
        )

    except Exception as e:
        error_response(str(e), 500)
